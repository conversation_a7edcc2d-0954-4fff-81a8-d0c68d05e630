import 'dart:async';
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/services/OrderMonitor.dart';
import 'package:emartdriver/ui/home/<USER>';
import 'package:emartdriver/ui/home/<USER>/calular_distance.dart';
import 'package:emartdriver/vendor_status_enum.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

/// Service responsible for handling all business logic related to the Home screen
/// This includes order management, location tracking, and Firebase operations
class HomeService {
  // Subscriptions
  StreamSubscription<QuerySnapshot>? _ordersSubscription;
  StreamSubscription<DocumentSnapshot>? _acceptedOrderSubscription;
  StreamSubscription<Position>? _positionStreamSubscription;
  StreamSubscription<QuerySnapshot>? _devolucaoPendenteSubscription;

  // Order monitoring
  final OrderMonitor _orderMonitor = OrderMonitor();
  
  // Current user
  final String currentUserId = FirebaseAuth.instance.currentUser?.uid ?? '';

  // State flags
  bool _showingNewOrderNotification = false;
  bool _devolucaoBottomsheetAtivo = false;

  // Callbacks
  Function(OrderModel)? onNewOrder;
  Function(QuerySnapshot)? onAvailableOrdersUpdate;
  Function(DocumentSnapshot)? onAcceptedOrderUpdate;
  Function(DocumentSnapshot)? onReturnOrderUpdate;
  Function(Position)? onPositionUpdate;
  Function()? onDevolucaoBottomsheetShow;
  Function()? onDevolucaoBottomsheetHide;

  // Getters
  bool get showingNewOrderNotification => _showingNewOrderNotification;
  bool get devolucaoBottomsheetAtivo => _devolucaoBottomsheetAtivo;

  /// Initialize the service with callbacks
  void initialize({
    Function(OrderModel)? onNewOrder,
    Function(QuerySnapshot)? onAvailableOrdersUpdate,
    Function(DocumentSnapshot)? onAcceptedOrderUpdate,
    Function(DocumentSnapshot)? onReturnOrderUpdate,
    Function(Position)? onPositionUpdate,
    Function()? onDevolucaoBottomsheetShow,
    Function()? onDevolucaoBottomsheetHide,
  }) {
    this.onNewOrder = onNewOrder;
    this.onAvailableOrdersUpdate = onAvailableOrdersUpdate;
    this.onAcceptedOrderUpdate = onAcceptedOrderUpdate;
    this.onReturnOrderUpdate = onReturnOrderUpdate;
    this.onPositionUpdate = onPositionUpdate;
    this.onDevolucaoBottomsheetShow = onDevolucaoBottomsheetShow;
    this.onDevolucaoBottomsheetHide = onDevolucaoBottomsheetHide;
  }

  /// Accept a new order
  Future<OrderModel?> acceptOrder(OrderModel order) async {
    try {
      final docRef = FirebaseFirestore.instance.collection(ORDERS).doc(order.id);

      await docRef.update({
        "entregador_id": currentUserId,
        "horaAceite": Timestamp.now(),
        "status": OrderStatus.driverAccepted.description,
      });

      final updatedDoc = await docRef.get();
      final updatedOrder = OrderModel.fromJson(updatedDoc.data()!);

      return updatedOrder;
    } catch (e) {
      log("Erro ao aceitar pedido: $e");
      return null;
    }
  }

  /// Check for accepted orders
  Future<void> checkForAcceptedOrder() async {
    try {
      // Check for return orders first
      final returnQuery = FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: currentUserId)
          .where('status', isEqualTo: OrderStatus.delivered.description)
          .where('has_return', isEqualTo: true)
          .limit(1);

      final returnSnapshot = await returnQuery.get();

      log("Verificando pedidos de devolução: ${returnSnapshot.docs.length} encontrados");

      if (returnSnapshot.docs.isNotEmpty) {
        final orderId = returnSnapshot.docs.first.id;
        log("Pedido de devolução encontrado com ID: $orderId");
        listenToReturnOrder(orderId);
        return;
      }

      // Check for regular accepted orders
      final query = FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: currentUserId)
          .where('status', whereIn: [
        OrderStatus.driverAccepted.description,
        OrderStatus.driverOnTheWay.description,
        OrderStatus.driverPending.description
      ]).limit(1);

      final snapshot = await query.get();

      if (snapshot.docs.isNotEmpty) {
        final orderId = snapshot.docs.first.id;
        listenToAcceptedOrder(orderId);
      } else {
        listenToAvailableOrders();
      }
    } catch (e) {
      log("Error checking for accepted orders: $e");
    }
  }

  /// Listen to accepted order updates
  void listenToAcceptedOrder(String orderId) async {
    _ordersSubscription?.cancel();

    try {
      final docRef = FirebaseFirestore.instance.collection(ORDERS).doc(orderId);
      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        onAcceptedOrderUpdate?.call(docSnapshot);

        _acceptedOrderSubscription = docRef.snapshots().listen((docSnapshot) {
          if (docSnapshot.exists) {
            onAcceptedOrderUpdate?.call(docSnapshot);
          } else {
            listenToAvailableOrders();
          }
        }, onError: (e) {
          log("Error listening to accepted order: $e");
        });
      } else {
        listenToAvailableOrders();
      }
    } catch (e) {
      log("Error fetching initial accepted order: $e");
      listenToAvailableOrders();
    }
  }

  /// Listen to available orders
  void listenToAvailableOrders() async {
    _acceptedOrderSubscription?.cancel();

    try {
      final query = FirebaseFirestore.instance
          .collection(ORDERS)
          .where('status', isEqualTo: OrderStatus.driverSearching.description);

      final snapshot = await query.get();
      onAvailableOrdersUpdate?.call(snapshot);

      _ordersSubscription = query.snapshots().listen((querySnapshot) {
        onAvailableOrdersUpdate?.call(querySnapshot);
      }, onError: (e) {
        log("Error listening to available orders: $e");
      });
    } catch (e) {
      log("Error fetching initial available orders: $e");
    }
  }

  /// Listen to return order updates
  void listenToReturnOrder(String orderId) async {
    _ordersSubscription?.cancel();

    try {
      final docRef = FirebaseFirestore.instance.collection(ORDERS).doc(orderId);
      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        onReturnOrderUpdate?.call(docSnapshot);

        _acceptedOrderSubscription = docRef.snapshots().listen((docSnapshot) {
          if (docSnapshot.exists) {
            onReturnOrderUpdate?.call(docSnapshot);
          } else {
            listenToAvailableOrders();
          }
        }, onError: (e) {
          log("Error listening to return order: $e");
        });
      } else {
        listenToAvailableOrders();
      }
    } catch (e) {
      log("Error fetching initial return order: $e");
      listenToAvailableOrders();
    }
  }

  /// Start position updates
  void startPositionUpdates() {
    _positionStreamSubscription?.cancel();

    const LocationSettings locationSettings = LocationSettings(
      accuracy: LocationAccuracy.high,
      distanceFilter: 10,
    );

    _positionStreamSubscription = Geolocator.getPositionStream(
      locationSettings: locationSettings,
    ).listen((Position position) {
      onPositionUpdate?.call(position);
    });
  }

  /// Initialize order monitor
  void initializeOrderMonitor({
    required LatLng? currentPosition,
    required Function(OrderModel) onNewOrderCallback,
  }) {
    if (currentPosition != null) {
      _orderMonitor.initialize(
        onNewOrder: onNewOrderCallback,
        currentPosition: currentPosition,
        currentUserId: currentUserId,
      );
    }
  }

  /// Check if there's a pending return order
  Future<bool> temPedidoComDevolucaoPendente() async {
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection(ORDERS)
          .where('entregador_id', isEqualTo: currentUserId)
          .where('has_return', isEqualTo: true)
          .where('status', isEqualTo: OrderStatus.returned.description)
          .limit(1)
          .get();

      return snapshot.docs.isNotEmpty;
    } catch (e) {
      log("Erro ao verificar pedidos com devolução pendente: $e");
      return false;
    }
  }

  /// Start monitoring pending returns
  void iniciarMonitoramentoDevolucaoPendente() {
    _devolucaoPendenteSubscription?.cancel();

    _devolucaoPendenteSubscription = FirebaseFirestore.instance
        .collection(ORDERS)
        .where('entregador_id', isEqualTo: currentUserId)
        .where('has_return', isEqualTo: true)
        .snapshots()
        .listen((snapshot) {
      final pedidosDevolucaoPendente = snapshot.docs.where((doc) {
        final data = doc.data();
        final status = data['status'] as String?;
        return status == OrderStatus.returned.description;
      }).toList();

      final temDevolucaoPendente = pedidosDevolucaoPendente.isNotEmpty;

      log("Monitoramento devolução: temDevolucaoPendente=$temDevolucaoPendente, total_has_return=${snapshot.docs.length}, pendentes=${pedidosDevolucaoPendente.length}");

      if (temDevolucaoPendente && !_devolucaoBottomsheetAtivo) {
        log("Exibindo bottomsheet de devolução pendente");
        _devolucaoBottomsheetAtivo = true;
        onDevolucaoBottomsheetShow?.call();
      } else if (!temDevolucaoPendente && _devolucaoBottomsheetAtivo) {
        log("Fechando bottomsheet - devolução resolvida");
        _devolucaoBottomsheetAtivo = false;
        onDevolucaoBottomsheetHide?.call();
      }
    }, onError: (error) {
      log("Erro no monitoramento de devolução: $error");
    });
  }

  /// Set showing new order notification state
  void setShowingNewOrderNotification(bool value) {
    _showingNewOrderNotification = value;
  }

  /// Dispose all resources
  void dispose() {
    _ordersSubscription?.cancel();
    _acceptedOrderSubscription?.cancel();
    _positionStreamSubscription?.cancel();
    _devolucaoPendenteSubscription?.cancel();
    _orderMonitor.dispose();
  }
}
