import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

/// Widget that displays the floating action buttons for map controls
class MapControls extends StatelessWidget {
  final GoogleMapController? mapController;
  final VoidCallback? onCenterLocation;
  final VoidCallback? onReloadMap;

  const MapControls({
    super.key,
    this.mapController,
    this.onCenterLocation,
    this.onReloadMap,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      right: 5,
      top: 6,
      child: Column(
        children: [
          FloatingActionButton(
            heroTag: 'zoomInButton',
            mini: false,
            backgroundColor: Colors.white,
            onPressed: _zoomIn,
            child: const Icon(
              Icons.zoom_in_outlined,
              color: Color(0xff425799),
            ),
          ),
          const SizedBox(height: 16),
          FloatingActionButton(
            heroTag: "zoomOutButton",
            mini: false,
            backgroundColor: Colors.white,
            onPressed: _zoomOut,
            child: const Icon(
              Icons.zoom_out_outlined,
              color: Color(0xff425799),
            ),
          ),
          const SizedBox(height: 16),
          FloatingActionButton(
            heroTag: "centerLocationButton",
            mini: false,
            backgroundColor: Colors.white,
            onPressed: onCenterLocation,
            child: const Icon(
              Icons.my_location,
              color: Color(0xff425799),
            ),
          ),
          const SizedBox(height: 16),
          FloatingActionButton(
            heroTag: "reloadMapButton",
            mini: false,
            backgroundColor: Colors.white,
            onPressed: onReloadMap,
            child: const Icon(
              Icons.refresh,
              color: Color(0xff425799),
            ),
          ),
        ],
      ),
    );
  }

  void _zoomIn() {
    if (mapController == null) return;
    
    try {
      mapController!.getZoomLevel().then((currentZoom) {
        double newZoom = currentZoom + 1.0;
        newZoom = newZoom > 20.0 ? 20.0 : newZoom;

        mapController!.animateCamera(
          CameraUpdate.zoomTo(newZoom),
        );
      });
    } catch (e) {
      log("Erro ao aumentar zoom: $e");
    }
  }

  void _zoomOut() {
    if (mapController == null) return;
    
    try {
      mapController!.getZoomLevel().then((currentZoom) {
        double newZoom = currentZoom - 1.0;
        newZoom = newZoom < 2.0 ? 2.0 : newZoom;

        mapController!.animateCamera(
          CameraUpdate.zoomTo(newZoom),
        );
      });
    } catch (e) {
      log("Erro ao diminuir zoom: $e");
    }
  }
}
