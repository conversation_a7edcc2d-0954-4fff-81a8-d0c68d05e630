import 'dart:async';
import 'dart:developer';
import 'dart:ui' as ui;

import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class LocationService {
  StreamSubscription<Position>? _positionStreamSubscription;
  LatLng? _currentPosition;
  BitmapDescriptor? _deliveryPersonIcon;

  Function(LatLng)? onPositionChanged;

  LatLng? get currentPosition => _currentPosition;
  BitmapDescriptor? get deliveryPersonIcon => _deliveryPersonIcon;

  Future<LatLng?> determinePosition() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        log("Location services not enabled");
        return null;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.deniedForever) {
          log("Location permission denied forever");
          return null;
        }
      }

      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
        ),
      );

      _currentPosition = LatLng(position.latitude, position.longitude);

      log("Current position determined: ${position.latitude}, ${position.longitude}");

      return _currentPosition;
    } catch (e) {
      log("Error determining position: $e");
      return null;
    }
  }

  void startPositionUpdates() {
    _positionStreamSubscription?.cancel();

    _positionStreamSubscription = Geolocator.getPositionStream(
      locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10,
      ),
    ).listen((Position position) {
      _currentPosition = LatLng(position.latitude, position.longitude);

      if (onPositionChanged != null) {
        onPositionChanged!(_currentPosition!);
      }
    });
  }

  /// Load custom marker icon
  Future<void> loadCustomMarkerIcon() async {
    try {
      final Uint8List markerIcon =
          await getBytesFromAsset('assets/images/motoentregador.png', 40);
      _deliveryPersonIcon = BitmapDescriptor.bytes(markerIcon);

      _deliveryPersonIcon ??=
          BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure);
    } catch (e) {
      log("Error in loadCustomMarkerIcon: $e");
      _deliveryPersonIcon =
          BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure);
    }
  }

  /// Get bytes from asset
  Future<Uint8List> getBytesFromAsset(String path, int width) async {
    ByteData data = await rootBundle.load(path);
    ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
        targetWidth: width);
    ui.FrameInfo fi = await codec.getNextFrame();
    return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
        .buffer
        .asUint8List();
  }

  /// Create current position marker
  Marker createCurrentPositionMarker() {
    if (_currentPosition == null) {
      throw Exception("Current position is null");
    }

    return Marker(
      markerId: const MarkerId('entregador'),
      position: _currentPosition!,
      icon: _deliveryPersonIcon ?? BitmapDescriptor.defaultMarker,
      rotation: _currentPosition!.latitude, // Apenas um valor exemplo
    );
  }

  /// Get LatLng from GeoPoint
  LatLng? getLatLng(dynamic geoPoint) {
    if (geoPoint == null) return null;
    return LatLng(geoPoint.latitude, geoPoint.longitude);
  }

  /// Update current position
  void updateCurrentPosition(LatLng position) {
    _currentPosition = position;
  }

  void dispose() {
    _positionStreamSubscription?.cancel();
  }
}
