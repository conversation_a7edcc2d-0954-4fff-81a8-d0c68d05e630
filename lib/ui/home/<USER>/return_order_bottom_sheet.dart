import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:emartdriver/constants.dart';
import 'package:emartdriver/model/OrderModel.dart';
import 'package:emartdriver/services/helper.dart';
import 'package:emartdriver/vendor_status_enum.dart';
import 'package:flutter/material.dart';

/// Bottom sheet component for handling return orders
class ReturnOrderBottomSheet extends StatelessWidget {
  final OrderModel returnOrder;
  final VoidCallback? onReturnConfirmed;

  const ReturnOrderBottomSheet({
    super.key,
    required this.returnOrder,
    this.onReturnConfirmed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.40,
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const SizedBox(height: 20),
              GestureDetector(
                onTap: () {},
                child: Container(
                  width: 50,
                  height: 5,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              )
            ],
          ),
          const SizedBox(height: 20),
          Text(
            'Entrega com Retorno - ${returnOrder.vendor.title}',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xff425799),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 15),
          Container(
            padding: const EdgeInsets.all(10),
            decoration: const BoxDecoration(
              color: Color.fromARGB(39, 201, 212, 247),
            ),
            width: MediaQuery.of(context).size.width,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset('assets/images/caixa.png', height: 30, width: 30),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    "Retorne ao estabelecimento para finalizar a entrega",
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 10),
          Container(
            padding: const EdgeInsets.all(10),
            decoration: const BoxDecoration(
              color: Color.fromARGB(39, 247, 201, 201),
            ),
            width: MediaQuery.of(context).size.width,
            child: Column(
              children: [
                Text(
                  "Loja: ${returnOrder.vendor.title}",
                  style: const TextStyle(
                      fontSize: 14, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 5),
                Text(
                  "${returnOrder.vendor.address_store?.logradouro ?? ""}, ${returnOrder.vendor.address_store?.numero ?? ""}, ${returnOrder.vendor.address_store?.bairro ?? ""}, ${returnOrder.vendor.address_store?.cidade ?? ""}",
                  style: const TextStyle(fontSize: 12),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Expanded(
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onPressed: () => _confirmReturn(context),
                  child: const Text(
                    "Confirmar Devolução",
                    style: TextStyle(fontSize: 14, color: Colors.white),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _confirmReturn(BuildContext context) async {
    Navigator.pop(context);
    try {
      final docRef = FirebaseFirestore.instance
          .collection(ORDERS)
          .doc(returnOrder.id);

      await showProgress(context, 'Confirmando Devolução', false);

      await docRef.update({
        'status': OrderStatus.returned.description,
      });

      await Future.delayed(const Duration(seconds: 2));
      await hideProgress();

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text("Devolução confirmada com sucesso!"),
          ),
        );

        onReturnConfirmed?.call();
      }
    } catch (e) {
      hideProgress();
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Erro ao confirmar devolução: $e")),
        );
      }
    }
  }

  /// Show the return order bottom sheet
  static void show({
    required BuildContext context,
    required OrderModel returnOrder,
    VoidCallback? onReturnConfirmed,
  }) {
    showModalBottomSheet(
      enableDrag: true,
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return ReturnOrderBottomSheet(
          returnOrder: returnOrder,
          onReturnConfirmed: onReturnConfirmed,
        );
      },
    );
  }
}
